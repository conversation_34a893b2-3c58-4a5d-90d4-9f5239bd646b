{"version": "0.1.0", "generated_time": "2025-07-15T09:55:49.648049", "overall_comparison": {"model_auc": 0.5, "traditional_auc": 1.0, "improvement": -0.5, "improvement_pct": -50.0, "winner": "traditional"}, "subset_analysis": {"all": {"subset_name": "全量样本", "sample_count": 159, "traditional_auc": 1.0, "model_auc": 0.5, "auc_improvement": -0.5, "improvement_pct": -50.0}, "train": {"subset_name": "训练集", "sample_count": 111, "traditional_auc": 0.9999999999999999, "model_auc": 0.5, "auc_improvement": -0.****************, "improvement_pct": -49.999999999999986}, "test": {"subset_name": "测试集", "sample_count": 48, "traditional_auc": 1.0, "model_auc": 0.5, "auc_improvement": -0.5, "improvement_pct": -50.0}}, "data_sources": {"evaluation_results": "evaluation/evaluation_results.json", "compare_data": "evaluation/model_scorecard_compare.csv", "feature_weights": "feature/feature_weights.csv", "iv_ranking": "binning/iv_ranking.csv"}, "usage_note": "此文件整合了所有对比相关数据，供对比脚本使用"}