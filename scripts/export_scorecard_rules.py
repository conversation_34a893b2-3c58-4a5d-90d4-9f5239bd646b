#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型规则导出脚本
将训练好的分箱、WOE和模型权重导出为单个JSON文件，
以便于非Python的生产环境（如Node.js规则引擎）加载和使用。
"""

import os
import json
import joblib
import pandas as pd
import numpy as np
import argparse
from pathlib import Path
import pickle
from datetime import datetime
import math
import pandas as pd
from innovation_model_investigation.models.scorecard import ScorecardModel
from innovation_model_investigation.utils.config import Config
from innovation_model_investigation.features.binning import OptimalBinning


def export_scorecard_rules(model_version, output_dir, data_dictionary_path):
    """
    从保存的模型和分箱结果中导出评分卡规则为JSON和Markdown文件。

    :param model_version: 模型版本号
    :param output_dir: 导出文件的目录
    :param data_dictionary_path: 数据字典路径
    """
    # 构建路径
    model_path = os.path.join(output_dir, model_version, "models", "scorecard_model.pkl")
    binning_path = os.path.join(output_dir, model_version, "binning", "binning_results.pkl")
    json_rules_path = os.path.join(output_dir, model_version, "evaluation", "scorecard_rules.json")
    md_rules_path = os.path.join(output_dir, model_version, "evaluation", "Scorecard.md")

    # 加载模型和分箱器
    with open(model_path, "rb") as f:
        model_data = pickle.load(f)
    model = ScorecardModel()
    model.model = model_data['model']
    model.scaler = model_data['scaler']
    model.feature_names = model_data['feature_names']
    model.is_fitted = model_data['is_fitted']

    binner = OptimalBinning()
    binner.load_binning_results(binning_path)

    # 提取评分卡参数
    config = Config()
    scorecard_params = config.get('scorecard_parameters')
    base_score = scorecard_params['base_score']
    pdo = scorecard_params['pdo']

    # 提取模型系数和截距
    weights_df = model.get_feature_weights()
    coefficients = dict(zip(weights_df['feature_name'], weights_df['coefficient']))
    intercept = model.get_intercept()
    feature_names = model.feature_names

    # 构建规则字典
    rules = {
        "version": model_version,
        "exported_at": datetime.now().isoformat(),
        "scorecard_parameters": {
            "base_score": base_score,
            "pdo": pdo,
            "intercept": intercept
        },
        "feature_rules": {}
    }

    # 填充每个特征的规则
    for i, feature_name in enumerate(feature_names):
        feature_bins = binner.binning_results[feature_name]['binning_table']
        rules["feature_rules"][feature_name] = {
            "weight": coefficients[feature_name],
            "bins": [{"bin_name": row['Bin'], "woe": row['WoE']} for _, row in feature_bins.iterrows()]
        }

    # 保存为JSON文件
    os.makedirs(os.path.dirname(json_rules_path), exist_ok=True)
    with open(json_rules_path, "w") as f:
        json.dump(rules, f, indent=4)
    print(f"Scorecard rules exported to {json_rules_path}")

    # 生成并保存Markdown评分卡
    export_scorecard_to_markdown(rules, data_dictionary_path, md_rules_path)


def export_scorecard_to_markdown(scorecard_rules, data_dictionary_path, output_path):
    """
    Exports the scorecard rules to a Markdown file with Chinese feature names and grouped rows.
    """
    try:
        data_dict = pd.read_csv(data_dictionary_path)
        feature_map = dict(zip(data_dict['indicator_code'], data_dict['indicator_name']))
    except FileNotFoundError:
        print(f"Warning: Data dictionary not found at {data_dictionary_path}. Using English names only.")
        feature_map = {}

    params = scorecard_rules['scorecard_parameters']
    base_score = params['base_score']
    pdo = params['pdo']
    intercept = params['intercept']
    feature_rules = scorecard_rules['feature_rules']

    factor = pdo / math.log(2)
    offset = base_score - (factor * intercept)

    active_features = {k: v for k, v in feature_rules.items() if v['weight'] != 0}
    n_features = len(active_features)

    markdown = f"# 模型评分卡 (v{scorecard_rules['version']})\n\n"
    markdown += f"**基础分:** {base_score}\n"
    markdown += f"**PDO (Points to Double the Odds):** {pdo}\n\n"
    markdown += "| 特征 (英文) | 特征 (中文) | 取值范围 | WOE | 分数 |\n"
    markdown += "|---|---|---|---|---|\n"
    
    base_points = offset / n_features if n_features > 0 else offset
    markdown += f"| **基础分** | - | - | - | **{base_points:.2f}** |\n"

    for feature, rules in active_features.items():
        weight = rules['weight']
        chinese_name = feature_map.get(feature, feature)
        
        bins = [b for b in rules['bins'] if not (b['bin_name'] in ["", "Special", "Missing"] and b['woe'] == 0)]
        
        if not bins:
            continue

        for i, b in enumerate(bins):
            bin_name = b['bin_name']
            try:
                woe = float(b['woe'])
            except (ValueError, TypeError):
                woe = 0.0
            score = -(weight * woe) * factor
            
            if i == 0:
                markdown += f"| {feature} | {chinese_name} | {bin_name} | {woe:.4f} | {score:.2f} |\n"
            else:
                markdown += f"| | | {bin_name} | {woe:.4f} | {score:.2f} |\n"

    with open(output_path, 'w') as f:
        f.write(markdown)
    print(f"Scorecard.md generated successfully at {output_path}")


if __name__ == "__main__":
    export_scorecard_rules("0.2.0", "outputs", "data/data_dictionary.csv")

def _format_bin_name(bin_label):
    """
    将分箱标签（可能为Interval对象或字符串）格式化为optbinning输出的字符串形式。
    """
    if isinstance(bin_label, str):
        # optbinning的Bin列已经是字符串，直接返回
        return bin_label
    if pd.isna(bin_label):
        return "Missing"

    # 处理Interval对象
    # optbinning对于数值型特征，通常是左闭右开区间 [a, b)
    left_bracket = '['
    right_bracket = ')'

    left_val = '-inf' if bin_label.left == -np.inf else f"{bin_label.left:.2f}"
    right_val = 'inf' if bin_label.right == np.inf else f"{bin_label.right:.2f}"

    return f"{left_bracket}{left_val}, {right_val}{right_bracket}"

def find_latest_version_dir():
    """查找最新的模型版本目录"""
    outputs_dir = Path("outputs")
    if not outputs_dir.exists():
        print("❌ outputs目录不存在！")
        return None

    version_dirs = [d for d in outputs_dir.iterdir() if d.is_dir() and d.name[0].isdigit()]
    if not version_dirs:
        print("❌ 未找到任何版本输出目录！")
        return None

    version_dirs.sort(key=lambda p: [int(x) for x in p.name.split('.') if x.isdigit()], reverse=True)
    return version_dirs[0]

def main():
    parser = argparse.ArgumentParser(description="将评分卡模型导出为JSON规则文件")
    parser.add_argument('--version', type=str, help='指定要导出的模型版本 (例如: 0.1.0)。如果未指定，则使用最新版本。')
    args = parser.parse_args()

    print("🚀 开始导出评分卡规则...")
    
    if args.version:
        version_dir = Path(f"outputs/{args.version}")
        if not version_dir.exists():
            print(f"❌ 指定的版本目录不存在: {version_dir}")
            return
    else:
        print("🔍 未指定版本，正在查找最新版本...")
        version_dir = find_latest_version_dir()
        if not version_dir:
            return
    
    print(f"📂 使用版本: {version_dir.name}")

    # --- 1. 定义文件路径 ---
    binning_path = version_dir / "binning/binning_results.pkl"
    woe_encoder_path = version_dir / "models/woe_encoder.pkl"
    scorecard_model_path = version_dir / "models/scorecard_model.pkl"
    output_json_path = version_dir / "evaluation/scorecard_rules.json"

    # --- 2. 检查所有必需文件是否存在 ---
    required_files = [binning_path, woe_encoder_path, scorecard_model_path]
    for f in required_files:
        if not f.exists():
            print(f"❌ 必需文件不存在，无法导出: {f}")
            return

    print("✅ 所有必需的模块文件均已找到。")

    # --- 3. 加载模型组件 ---
    print("🔄 正在加载模型组件...")
    try:
        binning_results = joblib.load(binning_path)
        woe_encoder_data = joblib.load(woe_encoder_path)
        woe_mappings = woe_encoder_data['woe_mappings']
        scorecard_data = joblib.load(scorecard_model_path)
        model = scorecard_data['model']
        feature_names = scorecard_data['feature_names']
    except Exception as e:
        print(f"❌ 加载PKL文件时出错: {e}")
        return

    print("✅ 模型组件加载成功。")

    # --- 4. 提取模型权重和截距 ---
    print("⚖️ 正在提取模型权重和参数...")
    weights = pd.Series(model.coef_[0], index=feature_names)
    intercept = model.intercept_[0]

    # --- 5. 构建最终的JSON对象 ---
    feature_rules = {}

    for feature in feature_names:
        if feature not in binning_results:
            print(f"⚠️ 警告: 特征 '{feature}' 在分箱结果中未找到，已跳过。")
            continue
        
        # 获取分箱信息
        binning_table = binning_results[feature]['binning_table']
        
        # 获取WOE信息
        woe_map_raw = woe_mappings.get(feature, {}).get('woe_mapping', {})
        print(f"DEBUG: Feature {feature}, woe_map_raw keys: {[str(k) for k in woe_map_raw.keys()]}")
        # 将woe_map的键（可能为Interval对象）转换为optbinning的字符串形式
        woe_map = {_format_bin_name(k): v for k, v in woe_map_raw.items()}
        print(f"DEBUG: Feature {feature}, woe_map keys (formatted): {list(woe_map.keys())}")
        
        bins_list = []
        for i, row in binning_table.iterrows():
            bin_name = _format_bin_name(row['Bin'])
            print(f"DEBUG: Feature {feature}, bin_name from binning_table: {bin_name}")
            # 从WOE Map中找到对应的WOE值
            woe_value = woe_map.get(bin_name, 0.0) # 如果找不到，默认为0.0
            print(f"DEBUG: Feature {feature}, bin_name: {bin_name}, woe_value: {woe_value}")
            
            bins_list.append({
                "bin_name": bin_name,
                "woe": float(woe_value)
            })
        
        feature_rules[feature] = {
            "weight": float(weights.get(feature, 0.0)),
            "bins": bins_list
        }

    # 组装完整的JSON
    scorecard_json = {
        "version": version_dir.name,
        "exported_at": pd.Timestamp.now().isoformat(),
        "scorecard_parameters": {
            "base_score": 600,
            "pdo": 20,
            "intercept": float(intercept)
        },
        "feature_rules": feature_rules
    }
    
    print("🔩 JSON规则组装完成。")

    # --- 6. 保存为JSON文件 ---
    try:
        with open(output_json_path, 'w', encoding='utf-8') as f:
            json.dump(scorecard_json, f, ensure_ascii=False, indent=4)
        print(f"💾 成功！评分卡规则已导出到: {output_json_path}")
    except Exception as e:
        print(f"❌ 保存JSON文件时出错: {e}")

    print("\n🎉 导出流程执行完成!")

if __name__ == "__main__":
    main()
